{"timestamp": "2025-06-16T18:10:40.934116", "summary": {"passed": 12, "failed": 5, "warnings": 0, "errors": 1, "total_execution_time": 49.79503655433655}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.3399639129638672, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.25354599952697754, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "FAILED", "severity": "MEDIUM", "details": "AI-tested 1 forms | Insights: 1 | Issues: 2 | AI: Basic security checks recommended", "execution_time": 2.0211892127990723, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.22710108757019043, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.7813479900360107, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.04397106170654297, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 1.0300228595733643, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.10416102409362793, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.038500070571899414, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.8615491390228271, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 15.533299922943115, "recommendations": [], "screenshot_path": ""}, {"test_name": "test_malicious_inputs", "status": "ERROR", "severity": "HIGH", "details": "Page.query_selector_all: Execution context was destroyed, most likely because of a navigation", "execution_time": 3.1622049808502197, "recommendations": ["Fix error in test_malicious_inputs: Page.query_selector_all: Execution context was destroyed, most likely because of a navigation"], "screenshot_path": "screenshots/screenshot_test_malicious_inputs_20250616_181014.png"}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 7.709958076477051, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 6.782788038253784, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 3.0111050605773926, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 5.561201095581055, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.26183009147644043, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 1.****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250616_181039.png"}]}