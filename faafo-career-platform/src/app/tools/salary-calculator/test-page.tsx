'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calculator, CheckCircle2 } from 'lucide-react';

// Simple test component to verify salary calculator functionality
export default function SalaryCalculatorTest() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testCalculation = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/tools/salary-calculator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          careerPath: 'Software Developer',
          experienceLevel: 'mid',
          location: 'San Francisco, CA',
          skills: ['JavaScript', 'React', 'Node.js'],
          education: 'bachelor',
          companySize: 'large',
          industry: 'technology',
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Test failed:', error);
      setResult({ success: false, error: 'Test failed' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Calculator className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Salary Calculator Test</h1>
        </div>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Test the salary calculator API functionality
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API Test</CardTitle>
          <CardDescription>
            Click the button below to test the salary calculation API with sample data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testCalculation} disabled={loading} className="w-full">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Testing...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                Test Salary Calculation
              </>
            )}
          </Button>

          {result && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  ) : (
                    <div className="h-5 w-5 bg-red-500 rounded-full" />
                  )}
                  Test Result
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto text-sm">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Test Parameters:</h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Career Path: Software Developer</li>
              <li>• Experience: Mid Level (3-5 years)</li>
              <li>• Location: San Francisco, CA</li>
              <li>• Skills: JavaScript, React, Node.js</li>
              <li>• Education: Bachelor's Degree</li>
              <li>• Company Size: Large</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
