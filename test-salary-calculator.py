#!/usr/bin/env python3
"""
Comprehensive Salary Calculator Testing with testerat
Advanced AI-powered testing for the FAAFO Career Platform Salary Calculator
"""

import sys
import os
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any

# Add testerat to path
sys.path.append('testerat')
from testerat import AIWebTester, TestConfig

class SalaryCalculatorTester:
    """Specialized tester for Salary Calculator functionality"""
    
    def __init__(self, base_url: str = "http://localhost:3006"):
        self.base_url = base_url
        self.salary_calculator_url = f"{base_url}/tools/salary-calculator"
        self.api_url = f"{base_url}/api/tools/salary-calculator"
        
        # Enhanced configuration for salary calculator testing
        self.config = TestConfig(
            headless=False,  # Show browser for detailed inspection
            viewport_width=1920,
            viewport_height=1080,
            timeout=30000,
            performance_thresholds={
                'load_time': 2000,     # Salary calculator should load fast
                'first_contentful_paint': 1000,
                'largest_contentful_paint': 2500
            },
            security_testing=True,
            edge_case_testing=True,
            ai_analysis=True
        )
        
        self.tester = AIWebTester(self.config)
        self.test_results = []
        
    def run_comprehensive_salary_calculator_tests(self):
        """Run all salary calculator tests"""
        print("🧮 Starting Comprehensive Salary Calculator Testing")
        print(f"🎯 Target URL: {self.salary_calculator_url}")
        print(f"🔗 API URL: {self.api_url}")
        print("=" * 80)
        
        try:
            # 1. Basic functionality tests
            self.test_page_accessibility()
            self.test_form_functionality()
            self.test_calculation_accuracy()
            
            # 2. API endpoint tests
            self.test_api_endpoints()
            self.test_api_validation()
            self.test_api_security()
            
            # 3. UI/UX tests
            self.test_responsive_design()
            self.test_user_experience()
            self.test_error_handling()
            
            # 4. Performance tests
            self.test_performance()
            self.test_load_handling()
            
            # 5. Security tests
            self.test_input_validation()
            self.test_xss_protection()
            self.test_rate_limiting()
            
            # 6. Edge case tests
            self.test_edge_cases()
            self.test_boundary_conditions()
            
            # 7. Integration tests
            self.test_tools_integration()
            self.test_navigation_integration()
            
            # Generate comprehensive report
            self.generate_salary_calculator_report()
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
            self.test_results.append({
                'test': 'Critical Error',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
        finally:
            self.cleanup()
    
    def test_page_accessibility(self):
        """Test salary calculator page accessibility"""
        print("\n🔍 Testing Page Accessibility...")
        
        try:
            page = self.tester.context.new_page()
            page.goto(self.salary_calculator_url)
            
            # Wait for page to load
            page.wait_for_selector('h1:has-text("Salary Calculator")', timeout=10000)
            
            # Test basic accessibility
            results = {
                'page_title': page.title(),
                'h1_present': bool(page.query_selector('h1')),
                'form_present': bool(page.query_selector('form')),
                'labels_present': len(page.query_selector_all('label')) > 0,
                'buttons_accessible': len(page.query_selector_all('button[type="submit"]')) > 0,
                'aria_labels': len(page.query_selector_all('[aria-label]')),
                'focus_management': True  # Will test interactively
            }
            
            # Test keyboard navigation
            page.keyboard.press('Tab')
            focused_element = page.evaluate('document.activeElement.tagName')
            results['keyboard_navigation'] = focused_element in ['BUTTON', 'SELECT', 'INPUT']
            
            self.test_results.append({
                'test': 'Page Accessibility',
                'status': 'PASSED' if all([results['h1_present'], results['form_present'], results['labels_present']]) else 'FAILED',
                'details': results,
                'timestamp': datetime.now().isoformat()
            })
            
            print(f"✅ Accessibility Test: {'PASSED' if results['form_present'] else 'FAILED'}")
            
        except Exception as e:
            print(f"❌ Accessibility Test Failed: {e}")
            self.test_results.append({
                'test': 'Page Accessibility',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    def test_form_functionality(self):
        """Test salary calculator form functionality"""
        print("\n📝 Testing Form Functionality...")
        
        try:
            page = self.tester.context.new_page()
            page.goto(self.salary_calculator_url)
            
            # Wait for form to load
            page.wait_for_selector('form', timeout=10000)
            
            # Test form elements
            career_path_select = page.query_selector('select, [role="combobox"]')
            experience_select = page.query_selector_all('select, [role="combobox"]')[1] if len(page.query_selector_all('select, [role="combobox"]')) > 1 else None
            location_select = page.query_selector_all('select, [role="combobox"]')[2] if len(page.query_selector_all('select, [role="combobox"]')) > 2 else None
            submit_button = page.query_selector('button[type="submit"]')
            
            results = {
                'career_path_field': bool(career_path_select),
                'experience_field': bool(experience_select),
                'location_field': bool(location_select),
                'submit_button': bool(submit_button),
                'form_validation': False,
                'calculation_works': False
            }
            
            # Test form interaction
            if career_path_select and submit_button:
                # Try to click career path dropdown (using modern select components)
                try:
                    # Look for trigger button or select element
                    trigger = page.query_selector('[role="combobox"], select, button:has-text("Select")')
                    if trigger:
                        trigger.click()
                        page.wait_for_timeout(1000)
                        
                        # Look for options
                        options = page.query_selector_all('[role="option"], option')
                        if options and len(options) > 0:
                            options[0].click()  # Select first option
                            results['career_path_selection'] = True
                        
                        # Try to submit and see if validation works
                        submit_button.click()
                        page.wait_for_timeout(2000)
                        
                        # Check if calculation result appears or validation message
                        result_element = page.query_selector('[data-testid="salary-result"], .salary-result, h4:has-text("Salary Estimate")')
                        error_element = page.query_selector('.error, [role="alert"], .text-red')
                        
                        results['calculation_works'] = bool(result_element)
                        results['form_validation'] = bool(error_element) or bool(result_element)
                        
                except Exception as form_error:
                    print(f"⚠️ Form interaction error: {form_error}")
                    results['form_interaction_error'] = str(form_error)
            
            status = 'PASSED' if results['career_path_field'] and results['submit_button'] else 'FAILED'
            
            self.test_results.append({
                'test': 'Form Functionality',
                'status': status,
                'details': results,
                'timestamp': datetime.now().isoformat()
            })
            
            print(f"✅ Form Test: {status}")
            
        except Exception as e:
            print(f"❌ Form Test Failed: {e}")
            self.test_results.append({
                'test': 'Form Functionality',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    def test_api_endpoints(self):
        """Test salary calculator API endpoints"""
        print("\n🔗 Testing API Endpoints...")
        
        try:
            # Test GET endpoint for metadata
            get_response = requests.get(f"{self.api_url}", timeout=10)
            get_results = {
                'status_code': get_response.status_code,
                'response_time': get_response.elapsed.total_seconds(),
                'has_data': 'data' in get_response.json() if get_response.status_code == 200 else False
            }
            
            # Test POST endpoint with valid data
            test_payload = {
                'careerPath': 'Software Developer',
                'experienceLevel': 'mid',
                'location': 'San Francisco, CA',
                'skills': ['JavaScript', 'React', 'Node.js'],
                'education': 'bachelor',
                'companySize': 'large',
                'industry': 'technology'
            }
            
            post_response = requests.post(
                f"{self.api_url}",
                json=test_payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            post_results = {
                'status_code': post_response.status_code,
                'response_time': post_response.elapsed.total_seconds(),
                'has_calculation': False,
                'has_recommendations': False,
                'salary_range_valid': False
            }
            
            if post_response.status_code == 200:
                data = post_response.json()
                if 'data' in data:
                    calc_data = data['data']
                    post_results['has_calculation'] = 'adjustedRange' in calc_data
                    post_results['has_recommendations'] = 'marketInsights' in calc_data and len(calc_data.get('marketInsights', {}).get('recommendations', [])) > 0
                    
                    if 'adjustedRange' in calc_data:
                        adj_range = calc_data['adjustedRange']
                        post_results['salary_range_valid'] = adj_range.get('min', 0) > 0 and adj_range.get('max', 0) > adj_range.get('min', 0)
            
            api_status = 'PASSED' if get_results['status_code'] == 200 and post_results['status_code'] == 200 else 'FAILED'
            
            self.test_results.append({
                'test': 'API Endpoints',
                'status': api_status,
                'details': {
                    'get_endpoint': get_results,
                    'post_endpoint': post_results
                },
                'timestamp': datetime.now().isoformat()
            })
            
            print(f"✅ API Test: {api_status}")
            print(f"   GET: {get_results['status_code']} ({get_results['response_time']:.2f}s)")
            print(f"   POST: {post_results['status_code']} ({post_results['response_time']:.2f}s)")
            
        except Exception as e:
            print(f"❌ API Test Failed: {e}")
            self.test_results.append({
                'test': 'API Endpoints',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    def test_calculation_accuracy(self):
        """Test salary calculation accuracy"""
        print("\n🧮 Testing Calculation Accuracy...")
        
        test_cases = [
            {
                'name': 'Entry Level Software Developer',
                'input': {
                    'careerPath': 'Software Developer',
                    'experienceLevel': 'entry',
                    'location': 'Other',
                    'skills': [],
                    'education': 'bachelor',
                    'companySize': 'medium'
                },
                'expected_range': (40000, 90000)  # Rough expected range
            },
            {
                'name': 'Senior Developer in SF',
                'input': {
                    'careerPath': 'Software Developer',
                    'experienceLevel': 'senior',
                    'location': 'San Francisco, CA',
                    'skills': ['JavaScript', 'React', 'Node.js', 'Python'],
                    'education': 'master',
                    'companySize': 'large'
                },
                'expected_range': (150000, 400000)  # High-end range for SF senior
            }
        ]
        
        accuracy_results = []
        
        try:
            for test_case in test_cases:
                response = requests.post(
                    f"{self.api_url}",
                    json=test_case['input'],
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()['data']
                    adj_range = data['adjustedRange']
                    actual_min = adj_range['min']
                    actual_max = adj_range['max']
                    expected_min, expected_max = test_case['expected_range']
                    
                    # Check if actual range is reasonable
                    range_reasonable = (
                        actual_min >= expected_min * 0.5 and  # Within 50% of expected minimum
                        actual_max <= expected_max * 1.5 and  # Within 150% of expected maximum
                        actual_min < actual_max  # Min is less than max
                    )
                    
                    accuracy_results.append({
                        'test_case': test_case['name'],
                        'actual_range': f"${actual_min:,} - ${actual_max:,}",
                        'expected_range': f"${expected_min:,} - ${expected_max:,}",
                        'reasonable': range_reasonable,
                        'confidence': data.get('confidence', 0),
                        'factors': data.get('factors', {})
                    })
                else:
                    accuracy_results.append({
                        'test_case': test_case['name'],
                        'error': f"API returned {response.status_code}",
                        'reasonable': False
                    })
            
            all_reasonable = all(result.get('reasonable', False) for result in accuracy_results)
            
            self.test_results.append({
                'test': 'Calculation Accuracy',
                'status': 'PASSED' if all_reasonable else 'FAILED',
                'details': accuracy_results,
                'timestamp': datetime.now().isoformat()
            })
            
            print(f"✅ Accuracy Test: {'PASSED' if all_reasonable else 'FAILED'}")
            for result in accuracy_results:
                print(f"   {result['test_case']}: {result.get('actual_range', 'ERROR')}")
            
        except Exception as e:
            print(f"❌ Accuracy Test Failed: {e}")
            self.test_results.append({
                'test': 'Calculation Accuracy',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    def test_security_and_validation(self):
        """Test input validation and security"""
        print("\n🔒 Testing Security and Validation...")
        
        malicious_inputs = [
            {'careerPath': '<script>alert("xss")</script>', 'experienceLevel': 'mid'},
            {'careerPath': 'Software Developer', 'experienceLevel': 'invalid_level'},
            {'careerPath': '', 'experienceLevel': 'mid'},  # Empty required field
            {'careerPath': 'A' * 1000, 'experienceLevel': 'mid'},  # Very long input
        ]
        
        security_results = []
        
        try:
            for malicious_input in malicious_inputs:
                response = requests.post(
                    f"{self.api_url}",
                    json=malicious_input,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                # Should return 400 for invalid inputs
                security_results.append({
                    'input': str(malicious_input)[:100],
                    'status_code': response.status_code,
                    'properly_rejected': response.status_code == 400,
                    'response_safe': '<script>' not in response.text if response.status_code == 200 else True
                })
            
            all_secure = all(result['properly_rejected'] or result['response_safe'] for result in security_results)
            
            self.test_results.append({
                'test': 'Security and Validation',
                'status': 'PASSED' if all_secure else 'FAILED',
                'details': security_results,
                'timestamp': datetime.now().isoformat()
            })
            
            print(f"✅ Security Test: {'PASSED' if all_secure else 'FAILED'}")
            
        except Exception as e:
            print(f"❌ Security Test Failed: {e}")
            self.test_results.append({
                'test': 'Security and Validation',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    def generate_salary_calculator_report(self):
        """Generate comprehensive test report"""
        print("\n📊 Generating Comprehensive Test Report...")
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASSED'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAILED'])
        
        report = {
            'test_summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            'test_results': self.test_results,
            'timestamp': datetime.now().isoformat(),
            'target_url': self.salary_calculator_url,
            'api_url': self.api_url
        }
        
        # Save JSON report
        with open('salary_calculator_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate HTML report
        html_report = self.generate_html_report(report)
        with open('salary_calculator_test_report.html', 'w') as f:
            f.write(html_report)
        
        print(f"\n📋 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📈 Success Rate: {report['test_summary']['success_rate']}")
        print(f"\n📄 Reports Generated:")
        print(f"   📊 JSON: salary_calculator_test_report.json")
        print(f"   🌐 HTML: salary_calculator_test_report.html")
        
        return report
    
    def generate_html_report(self, report):
        """Generate HTML test report"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Salary Calculator Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
                .metric {{ background: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
                .test-result {{ margin: 10px 0; padding: 15px; border-radius: 5px; }}
                .passed {{ background: #d4edda; border-left: 5px solid #28a745; }}
                .failed {{ background: #f8d7da; border-left: 5px solid #dc3545; }}
                .details {{ margin-top: 10px; font-size: 0.9em; color: #666; }}
                pre {{ background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧮 Salary Calculator Test Report</h1>
                <p><strong>Target URL:</strong> {report['target_url']}</p>
                <p><strong>API URL:</strong> {report['api_url']}</p>
                <p><strong>Generated:</strong> {report['timestamp']}</p>
            </div>
            
            <div class="summary">
                <div class="metric">
                    <h3>{report['test_summary']['total_tests']}</h3>
                    <p>Total Tests</p>
                </div>
                <div class="metric">
                    <h3>{report['test_summary']['passed']}</h3>
                    <p>Passed</p>
                </div>
                <div class="metric">
                    <h3>{report['test_summary']['failed']}</h3>
                    <p>Failed</p>
                </div>
                <div class="metric">
                    <h3>{report['test_summary']['success_rate']}</h3>
                    <p>Success Rate</p>
                </div>
            </div>
            
            <h2>Test Results</h2>
        """
        
        for result in report['test_results']:
            status_class = result['status'].lower()
            html += f"""
            <div class="test-result {status_class}">
                <h3>{result['test']} - {result['status']}</h3>
                <div class="details">
                    <p><strong>Timestamp:</strong> {result['timestamp']}</p>
                    {f"<p><strong>Error:</strong> {result['error']}</p>" if 'error' in result else ""}
                    {f"<pre>{json.dumps(result['details'], indent=2)}</pre>" if 'details' in result else ""}
                </div>
            </div>
            """
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def cleanup(self):
        """Clean up resources"""
        try:
            self.tester.cleanup()
        except:
            pass

def main():
    """Main execution function"""
    print("🚀 Starting Comprehensive Salary Calculator Testing")
    print("=" * 80)
    
    # Check if development server is running
    try:
        response = requests.get("http://localhost:3006", timeout=5)
        print("✅ Development server is running")
    except:
        print("❌ Development server not accessible at http://localhost:3006")
        print("   Please start the server with: npm run dev")
        return
    
    # Initialize and run tests
    tester = SalaryCalculatorTester()
    
    try:
        # Run basic tests first
        tester.test_page_accessibility()
        tester.test_form_functionality()
        tester.test_api_endpoints()
        tester.test_calculation_accuracy()
        tester.test_security_and_validation()
        
        # Generate final report
        tester.generate_salary_calculator_report()
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Critical error: {e}")
    finally:
        tester.cleanup()
        print("\n🏁 Testing completed!")

if __name__ == "__main__":
    main()
