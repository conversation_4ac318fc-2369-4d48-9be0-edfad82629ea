{"test_summary": {"total_tests": 17, "passed": 5, "failed": 12, "success_rate": "29.4%", "timestamp": "2025-06-16T17:35:32.413526"}, "api_url": "http://localhost:3006/api/tools/salary-calculator", "test_results": [{"test": "API Availability", "status": "PASSED", "details": {"status_code": 200, "response_time": 0.025134, "accessible": true}, "timestamp": "2025-06-16T17:35:30.547632"}, {"test": "GET Default metadata", "status": "PASSED", "details": {"endpoint": "", "status_code": 200, "response_time": 0.036805, "has_data": true, "data_structure": {"success": true, "data_type": "dict", "data_length": 0}}, "timestamp": "2025-06-16T17:35:30.625939"}, {"test": "GET Career paths data", "status": "PASSED", "details": {"endpoint": "?type=career-paths", "status_code": 200, "response_time": 0.069416, "has_data": true, "data_structure": {"success": true, "data_type": "list", "data_length": 10}}, "timestamp": "2025-06-16T17:35:30.699203"}, {"test": "GET Locations data", "status": "PASSED", "details": {"endpoint": "?type=locations", "status_code": 200, "response_time": 0.035556, "has_data": true, "data_structure": {"success": true, "data_type": "list", "data_length": 14}}, "timestamp": "2025-06-16T17:35:30.738249"}, {"test": "GET Experience levels data", "status": "PASSED", "details": {"endpoint": "?type=experience-levels", "status_code": 200, "response_time": 0.052129, "has_data": true, "data_structure": {"success": true, "data_type": "list", "data_length": 7}}, "timestamp": "2025-06-16T17:35:30.794152"}, {"test": "POST Validation - Valid Complete Request", "status": "FAILED", "details": {"expected_status": 200, "actual_status": 429, "response_time": 0.049885, "payload_size": 216}, "timestamp": "2025-06-16T17:35:30.846745"}, {"test": "POST Validation - Minimal Valid Request", "status": "FAILED", "details": {"expected_status": 200, "actual_status": 429, "response_time": 0.052581, "payload_size": 83}, "timestamp": "2025-06-16T17:35:30.902575"}, {"test": "POST Validation - Missing Required Field", "status": "FAILED", "details": {"expected_status": 400, "actual_status": 429, "response_time": 0.056196, "payload_size": 47}, "timestamp": "2025-06-16T17:35:30.961490"}, {"test": "POST Validation - Invalid Experience Level", "status": "FAILED", "details": {"expected_status": 400, "actual_status": 429, "response_time": 0.096102, "payload_size": 93}, "timestamp": "2025-06-16T17:35:31.069913"}, {"test": "POST Validation - Empty Career Path", "status": "FAILED", "details": {"expected_status": 400, "actual_status": 429, "response_time": 0.048677, "payload_size": 65}, "timestamp": "2025-06-16T17:35:31.120908"}, {"test": "Calculation Logic - Entry Level vs Senior Comparison", "status": "FAILED", "error": "Could not get both comparison results", "timestamp": "2025-06-16T17:35:31.305097"}, {"test": "Calculation Logic - Location Impact Test", "status": "FAILED", "error": "Could not get both comparison results", "timestamp": "2025-06-16T17:35:31.474738"}, {"test": "Security - XSS Attempt", "status": "FAILED", "details": {"status_code": 429, "safe_handling": false, "response_contains_input": false}, "timestamp": "2025-06-16T17:35:31.539754"}, {"test": "Security - SQL Injection Attempt", "status": "FAILED", "details": {"status_code": 429, "safe_handling": false, "response_contains_input": false}, "timestamp": "2025-06-16T17:35:31.615421"}, {"test": "Security - Very Long Input", "status": "FAILED", "details": {"status_code": 429, "safe_handling": false, "response_contains_input": false}, "timestamp": "2025-06-16T17:35:31.691175"}, {"test": "Security - Special Characters", "status": "FAILED", "details": {"status_code": 429, "safe_handling": false, "response_contains_input": false}, "timestamp": "2025-06-16T17:35:31.873744"}, {"test": "Performance Test", "status": "FAILED", "error": "No successful requests", "timestamp": "2025-06-16T17:35:32.413080"}]}