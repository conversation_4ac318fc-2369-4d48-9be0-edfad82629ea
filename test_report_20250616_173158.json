{"timestamp": "2025-06-16T17:31:58.595997", "summary": {"passed": 13, "failed": 5, "warnings": 0, "errors": 0, "total_execution_time": 46.3368353843689}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.19996213912963867, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.19473624229431152, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "FAILED", "severity": "MEDIUM", "details": "AI-tested 1 forms | Insights: 1 | Issues: 2 | AI: Basic security checks recommended", "execution_time": 0.8295621871948242, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.20844078063964844, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.8161919116973877, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.059348106384277344, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 1.1373918056488037, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.12894010543823242, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.04180788993835449, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 1.218461036682129, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 14.725728988647461, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.0728597640991211, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 7.618457794189453, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 7.421433687210083, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 3.091233968734741, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 6.718554973602295, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.26180100440979004, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250616_173157.png"}]}