#!/usr/bin/env python3
"""
Comprehensive API Testing for Salary Calculator
Tests all API endpoints, validation, security, and edge cases
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class SalaryCalculatorAPITester:
    def __init__(self, base_url: str = "http://localhost:3006"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/tools/salary-calculator"
        self.test_results = []
        
    def run_all_tests(self):
        """Run comprehensive API tests"""
        print("🧮 Starting Comprehensive Salary Calculator API Testing")
        print(f"🎯 API URL: {self.api_url}")
        print("=" * 80)
        
        # Test categories
        self.test_api_availability()
        self.test_get_endpoints()
        self.test_post_validation()
        self.test_calculation_accuracy()
        self.test_security_validation()
        # self.test_rate_limiting()  # Skip for now
        # self.test_edge_cases()     # Skip for now
        self.test_performance()
        
        # Generate report
        self.generate_report()
        
    def test_api_availability(self):
        """Test if API is accessible"""
        print("\n🔍 Testing API Availability...")
        
        try:
            response = requests.get(self.api_url, timeout=10)
            
            result = {
                'test': 'API Availability',
                'status': 'PASSED' if response.status_code in [200, 405] else 'FAILED',
                'details': {
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'accessible': True
                },
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ API Availability: {result['status']} ({response.status_code})")
            
        except Exception as e:
            result = {
                'test': 'API Availability',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            print(f"❌ API Availability: FAILED - {e}")
            
        self.test_results.append(result)
    
    def test_get_endpoints(self):
        """Test GET endpoints for metadata"""
        print("\n📊 Testing GET Endpoints...")
        
        endpoints = [
            ('', 'Default metadata'),
            ('?type=career-paths', 'Career paths data'),
            ('?type=locations', 'Locations data'),
            ('?type=experience-levels', 'Experience levels data')
        ]
        
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.api_url}{endpoint}", timeout=10)
                
                result = {
                    'test': f'GET {description}',
                    'status': 'PASSED' if response.status_code == 200 else 'FAILED',
                    'details': {
                        'endpoint': endpoint,
                        'status_code': response.status_code,
                        'response_time': response.elapsed.total_seconds(),
                        'has_data': False,
                        'data_structure': {}
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        result['details']['has_data'] = 'data' in data
                        result['details']['data_structure'] = {
                            'success': data.get('success', False),
                            'data_type': type(data.get('data', None)).__name__,
                            'data_length': len(data.get('data', [])) if isinstance(data.get('data'), list) else 0
                        }
                    except:
                        result['details']['json_valid'] = False
                
                print(f"✅ GET {description}: {result['status']} ({response.status_code})")
                
            except Exception as e:
                result = {
                    'test': f'GET {description}',
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                print(f"❌ GET {description}: FAILED - {e}")
            
            self.test_results.append(result)
    
    def test_post_validation(self):
        """Test POST endpoint validation"""
        print("\n✅ Testing POST Validation...")
        
        test_cases = [
            {
                'name': 'Valid Complete Request',
                'payload': {
                    'careerPath': 'Software Developer',
                    'experienceLevel': 'mid',
                    'location': 'San Francisco, CA',
                    'skills': ['JavaScript', 'React', 'Node.js'],
                    'education': 'bachelor',
                    'companySize': 'large',
                    'industry': 'technology'
                },
                'expected_status': 200
            },
            {
                'name': 'Minimal Valid Request',
                'payload': {
                    'careerPath': 'Software Developer',
                    'experienceLevel': 'mid',
                    'location': 'Other'
                },
                'expected_status': 200
            },
            {
                'name': 'Missing Required Field',
                'payload': {
                    'experienceLevel': 'mid',
                    'location': 'Other'
                },
                'expected_status': 400
            },
            {
                'name': 'Invalid Experience Level',
                'payload': {
                    'careerPath': 'Software Developer',
                    'experienceLevel': 'invalid_level',
                    'location': 'Other'
                },
                'expected_status': 400
            },
            {
                'name': 'Empty Career Path',
                'payload': {
                    'careerPath': '',
                    'experienceLevel': 'mid',
                    'location': 'Other'
                },
                'expected_status': 400
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(
                    self.api_url,
                    json=test_case['payload'],
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                status_correct = response.status_code == test_case['expected_status']
                
                result = {
                    'test': f"POST Validation - {test_case['name']}",
                    'status': 'PASSED' if status_correct else 'FAILED',
                    'details': {
                        'expected_status': test_case['expected_status'],
                        'actual_status': response.status_code,
                        'response_time': response.elapsed.total_seconds(),
                        'payload_size': len(json.dumps(test_case['payload']))
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        result['details']['has_calculation'] = 'data' in data and 'adjustedRange' in data.get('data', {})
                    except:
                        result['details']['json_valid'] = False
                
                print(f"✅ {test_case['name']}: {result['status']} ({response.status_code})")
                
            except Exception as e:
                result = {
                    'test': f"POST Validation - {test_case['name']}",
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                print(f"❌ {test_case['name']}: FAILED - {e}")
            
            self.test_results.append(result)
    
    def test_calculation_accuracy(self):
        """Test calculation accuracy and logic"""
        print("\n🧮 Testing Calculation Accuracy...")
        
        test_scenarios = [
            {
                'name': 'Entry Level vs Senior Comparison',
                'tests': [
                    {
                        'careerPath': 'Software Developer',
                        'experienceLevel': 'entry',
                        'location': 'Other',
                        'expected_range': 'lower'
                    },
                    {
                        'careerPath': 'Software Developer',
                        'experienceLevel': 'senior',
                        'location': 'Other',
                        'expected_range': 'higher'
                    }
                ]
            },
            {
                'name': 'Location Impact Test',
                'tests': [
                    {
                        'careerPath': 'Software Developer',
                        'experienceLevel': 'mid',
                        'location': 'Other',
                        'expected_range': 'lower'
                    },
                    {
                        'careerPath': 'Software Developer',
                        'experienceLevel': 'mid',
                        'location': 'San Francisco, CA',
                        'expected_range': 'higher'
                    }
                ]
            }
        ]
        
        for scenario in test_scenarios:
            try:
                results = []
                for test in scenario['tests']:
                    response = requests.post(
                        self.api_url,
                        json=test,
                        headers={'Content-Type': 'application/json'},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        data = response.json()['data']
                        adj_range = data['adjustedRange']
                        median = data.get('median', (adj_range['min'] + adj_range['max']) / 2)
                        results.append({
                            'config': test,
                            'median': median,
                            'range': adj_range
                        })
                
                # Analyze results
                if len(results) == 2:
                    comparison_correct = False
                    if scenario['tests'][0]['expected_range'] == 'lower' and scenario['tests'][1]['expected_range'] == 'higher':
                        comparison_correct = results[0]['median'] < results[1]['median']
                    elif scenario['tests'][0]['expected_range'] == 'higher' and scenario['tests'][1]['expected_range'] == 'lower':
                        comparison_correct = results[0]['median'] > results[1]['median']
                    
                    result = {
                        'test': f"Calculation Logic - {scenario['name']}",
                        'status': 'PASSED' if comparison_correct else 'FAILED',
                        'details': {
                            'comparison_correct': comparison_correct,
                            'results': results
                        },
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    print(f"✅ {scenario['name']}: {result['status']}")
                    print(f"   Lower: ${results[0]['median']:,.0f}, Higher: ${results[1]['median']:,.0f}")
                else:
                    result = {
                        'test': f"Calculation Logic - {scenario['name']}",
                        'status': 'FAILED',
                        'error': 'Could not get both comparison results',
                        'timestamp': datetime.now().isoformat()
                    }
                    print(f"❌ {scenario['name']}: FAILED - Incomplete results")
                
            except Exception as e:
                result = {
                    'test': f"Calculation Logic - {scenario['name']}",
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                print(f"❌ {scenario['name']}: FAILED - {e}")
            
            self.test_results.append(result)
    
    def test_security_validation(self):
        """Test security and input sanitization"""
        print("\n🔒 Testing Security Validation...")
        
        malicious_inputs = [
            {
                'name': 'XSS Attempt',
                'payload': {
                    'careerPath': '<script>alert("xss")</script>',
                    'experienceLevel': 'mid',
                    'location': 'Other'
                }
            },
            {
                'name': 'SQL Injection Attempt',
                'payload': {
                    'careerPath': "'; DROP TABLE users; --",
                    'experienceLevel': 'mid',
                    'location': 'Other'
                }
            },
            {
                'name': 'Very Long Input',
                'payload': {
                    'careerPath': 'A' * 10000,
                    'experienceLevel': 'mid',
                    'location': 'Other'
                }
            },
            {
                'name': 'Special Characters',
                'payload': {
                    'careerPath': '!@#$%^&*()_+{}|:"<>?[]\\;\',./',
                    'experienceLevel': 'mid',
                    'location': 'Other'
                }
            }
        ]
        
        for test_case in malicious_inputs:
            try:
                response = requests.post(
                    self.api_url,
                    json=test_case['payload'],
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                # Should return 400 for invalid inputs or handle safely
                safe_handling = response.status_code == 400 or (
                    response.status_code == 200 and 
                    '<script>' not in response.text and
                    'DROP TABLE' not in response.text
                )
                
                result = {
                    'test': f"Security - {test_case['name']}",
                    'status': 'PASSED' if safe_handling else 'FAILED',
                    'details': {
                        'status_code': response.status_code,
                        'safe_handling': safe_handling,
                        'response_contains_input': test_case['payload']['careerPath'][:50] in response.text
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                print(f"✅ {test_case['name']}: {result['status']} ({response.status_code})")
                
            except Exception as e:
                result = {
                    'test': f"Security - {test_case['name']}",
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                print(f"❌ {test_case['name']}: FAILED - {e}")
            
            self.test_results.append(result)
    
    def test_performance(self):
        """Test API performance"""
        print("\n⚡ Testing Performance...")
        
        try:
            # Test multiple requests
            response_times = []
            valid_payload = {
                'careerPath': 'Software Developer',
                'experienceLevel': 'mid',
                'location': 'San Francisco, CA',
                'skills': ['JavaScript', 'React'],
                'education': 'bachelor',
                'companySize': 'medium'
            }
            
            for i in range(5):
                start_time = time.time()
                response = requests.post(
                    self.api_url,
                    json=valid_payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    response_times.append(end_time - start_time)
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                max_time = max(response_times)
                min_time = min(response_times)
                
                # Performance thresholds
                performance_good = avg_time < 2.0 and max_time < 5.0
                
                result = {
                    'test': 'Performance Test',
                    'status': 'PASSED' if performance_good else 'FAILED',
                    'details': {
                        'average_response_time': avg_time,
                        'max_response_time': max_time,
                        'min_response_time': min_time,
                        'total_requests': len(response_times),
                        'performance_threshold_met': performance_good
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                print(f"✅ Performance: {result['status']}")
                print(f"   Avg: {avg_time:.2f}s, Max: {max_time:.2f}s, Min: {min_time:.2f}s")
            else:
                result = {
                    'test': 'Performance Test',
                    'status': 'FAILED',
                    'error': 'No successful requests',
                    'timestamp': datetime.now().isoformat()
                }
                print("❌ Performance: FAILED - No successful requests")
                
        except Exception as e:
            result = {
                'test': 'Performance Test',
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            print(f"❌ Performance: FAILED - {e}")
        
        self.test_results.append(result)
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n📊 Generating Test Report...")
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASSED'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAILED'])
        
        report = {
            'test_summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                'timestamp': datetime.now().isoformat()
            },
            'api_url': self.api_url,
            'test_results': self.test_results
        }
        
        # Save report
        with open('salary_calculator_api_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 API Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📈 Success Rate: {report['test_summary']['success_rate']}")
        print(f"\n📄 Report saved: salary_calculator_api_test_report.json")
        
        return report

def main():
    """Main execution"""
    print("🚀 Starting Salary Calculator API Testing")
    print("=" * 80)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:3006", timeout=10)
        print("✅ Development server is accessible")
    except Exception as e:
        print(f"❌ Development server not accessible at http://localhost:3006: {e}")
        print("   Please start the server with: npm run dev")
        return
    
    # Run tests
    tester = SalaryCalculatorAPITester()
    tester.run_all_tests()
    
    print("\n🏁 API Testing completed!")

if __name__ == "__main__":
    main()
